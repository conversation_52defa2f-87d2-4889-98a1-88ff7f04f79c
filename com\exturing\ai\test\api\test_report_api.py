# api.py

import traceback
from flask import Blueprint, request,jsonify
from com.exturing.ai.test.model.test_report import AccuracyReportQueryModel, AccuracyReportGroupQueryModel
from com.exturing.ai.test.comm.api_result import Api<PERSON><PERSON>ult
from com.exturing.ai.test.comm.comm_constant import URL_PREFIX
from com.exturing.ai.test.comm.log_tool import et_log
from com.exturing.ai.test.comm.result_code_enum import ResultCode
from com.exturing.ai.test.service.test_report_service import query_accuracy_rate, query_accuracy_rate_group, \
     get_superCLUE_auto_report, get_radar_chart_data, get_dimension_metrics_chart_data, get_category_metrics_line_chart_data, \
     get_dimension_metrics_line_chart_data

test_report = Blueprint('test_report', __name__)

# 准确率报告
@test_report.route(f'/{URL_PREFIX}/test-report/accuracy-rate', methods=['POST'])
def accuracy_rate():
    et_log.info("############test_report_accuracy-rate################")
    req_data = request.get_json()
    query_params = AccuracyReportQueryModel(**req_data)
    list = query_accuracy_rate(query_params)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, list).to_json()

# 准确率分组报告
@test_report.route(f'/{URL_PREFIX}/test-report/accuracy-rate-group', methods=['POST'])
def accuracy_rate_group():
    et_log.info("############test_report_accuracy-rate################")
    req_data = request.get_json()
    query_params = AccuracyReportGroupQueryModel(**req_data)

    if query_params.task_result_id is None:
        return ApiResult(ResultCode.PARAM_ERROR.code, ResultCode.PARAM_ERROR.msg, None).to_json()

    list = query_accuracy_rate_group(query_params)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, list).to_json()

# superCLUE-auto报告
@test_report.route(f'/{URL_PREFIX}/test-report/super-clue-auto', methods=['POST'])
def super_clue_auto_report():
    et_log.info("############super_clue_auto_report-rate################")
    req_data = request.get_json()
    plan_id = req_data.get("plan_id", "")
    if plan_id is None or len(plan_id) == 0:
        return ApiResult(ResultCode.PARAM_IS_BLANK.code, ResultCode.PARAM_IS_BLANK.msg, None).to_json()

    group_ds_results = get_superCLUE_auto_report(plan_id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, group_ds_results).to_json()

# 雷达图数据
@test_report.route(f'/{URL_PREFIX}/test-report/category/single-result', methods=['POST'])
def radar_chart():
    et_log.info("############test_report_category_result################")
    req_data = request.get_json()

    task_result_id = req_data.get("task_result_id", "")
    task_id = req_data.get("task_id", "")

    # # 至少需要提供task_result_id或task_id中的一个
    if (not task_result_id or len(task_result_id) == 0) and (not task_id or len(task_id) == 0):
        #     return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_result_id或task_id不能为空", {}).to_json()
        return jsonify({
        "code": ResultCode.PARAM_IS_BLANK.code,
        "msg": "task_result_id或task_id不能为空",
        "data": {}
        })

    radar_data = get_radar_chart_data(task_result_id=task_result_id, task_id=task_id)

    return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, radar_data).to_json()


# 各模块/维度准确率、召回率、F1分数对比柱状图数据
@test_report.route(f'/{URL_PREFIX}/test-report/category/single-result-group', methods=['POST'])
def dimension_metrics_chart():
    et_log.info("############test_report_category_result-group################")

    try:
        req_data = request.get_json()
        et_log.info(f"[PERF] API Request data: {req_data}")

        task_result_id = req_data.get("task_result_id", "")
        task_id = req_data.get("task_id", "")
        group_by = req_data.get("group_by")

        # 参数验证
        if (not task_result_id or len(task_result_id) == 0) and (not task_id or len(task_id) == 0):
            # return ApiResult(ResultCode.PARAM_IS_BLANK.code, "task_result_id或task_id不能为空", []).to_json()
            return jsonify({
            "code": ResultCode.PARAM_IS_BLANK.code,
            "msg": "task_result_id或task_id不能为空",
            "data": []
            })

        # 获取图表数据
        chart_data = get_dimension_metrics_chart_data(
            task_result_id=task_result_id,
            task_id=task_id,
            group_by=group_by
        )
        return ApiResult(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, chart_data).to_json()

    except Exception as e:
        et_log.error(f"dimension_metrics_chart error: {e}")
        import traceback
        et_log.error(traceback.format_exc())
        return ApiResult(ResultCode.FAIL.code, f"获取维度指标图表数据失败: {str(e)}", []).to_json()
    
@test_report.route(f'/{URL_PREFIX}/test-report/category/multi_result', methods=['POST'])
def category_metrics_line_chart():
    et_log.info("############test_report_category_metrics_line_chart################")
    try:
        req_data = request.get_json()
        et_log.info(f"Request data: {req_data}")

        # 支持单/多 ID，确保参数类型正确
        task_result_ids = req_data.get("task_result_ids", [])
        task_ids        = req_data.get("task_ids", [])
        single_tr       = req_data.get("task_result_id")
        single_t        = req_data.get("task_id")

        # 确保task_result_ids是列表类型
        if not isinstance(task_result_ids, list):
            task_result_ids = []

        # 确保task_ids是列表类型
        if not isinstance(task_ids, list):
            task_ids = []

        # 处理单个ID参数，转换为列表
        if not task_result_ids and single_tr:
            task_result_ids = [single_tr]
        if not task_ids and single_t:
            task_ids = [single_t]

        # 过滤掉空值
        task_result_ids = [id for id in task_result_ids if id and str(id).strip()]
        task_ids = [id for id in task_ids if id and str(id).strip()]

        # 参数校验
        if not task_result_ids and not task_ids:
            return jsonify({
                "code": ResultCode.PARAM_IS_BLANK.code,
                "msg": "task_result_ids或task_ids不能为空",
                "data": []
            })

        # 获取折线图数据
        line_chart_data = get_category_metrics_line_chart_data(
            task_result_ids=task_result_ids,
            task_ids=task_ids
        )

        return ApiResult(
            ResultCode.SUCCESS.code,
            ResultCode.SUCCESS.msg,
            line_chart_data
        ).to_json()

    except Exception as e:
        et_log.error(f"category_metrics_line_chart error: {e}")
        et_log.error(traceback.format_exc())
        return ApiResult(
            ResultCode.FAILURE.code,
            f"获取折线图数据失败: {str(e)}",
            []
        ).to_json()

# 某个分类（各模块\维度）在各个结果中的准确率、召回率、F1分数对比折线图
@test_report.route(f'/{URL_PREFIX}/test-report/category/multi-result-group', methods=['POST'])
def dimension_metrics_line_chart():
    et_log.info("############test_report_dimension_metrics_line_chart################")
    try:
        req_data = request.get_json()
        et_log.info(f"Request data: {req_data}")

        # 支持单/多 ID，确保参数类型正确
        task_result_ids = req_data.get("task_result_ids", [])
        task_ids        = req_data.get("task_ids", [])
        single_tr       = req_data.get("task_result_id")
        single_t        = req_data.get("task_id")
        group_by        = req_data.get("group_by", "dimension_id")  # 默认按维度分组
        group_name      = req_data.get("group_name")  # 指定的分组名称

        # 确保task_result_ids是列表类型
        if not isinstance(task_result_ids, list):
            task_result_ids = []

        # 确保task_ids是列表类型
        if not isinstance(task_ids, list):
            task_ids = []

        # 处理单个ID参数，转换为列表
        if not task_result_ids and single_tr:
            task_result_ids = [single_tr]
        if not task_ids and single_t:
            task_ids = [single_t]

        # 过滤掉空值
        task_result_ids = [id for id in task_result_ids if id and str(id).strip()]
        task_ids = [id for id in task_ids if id and str(id).strip()]

        # 参数校验
        if not task_result_ids and not task_ids:
            return jsonify({
                "code": ResultCode.PARAM_IS_BLANK.code,
                "msg": "task_result_ids或task_ids不能为空",
                "data": []
            })

        if not group_name:
            return jsonify({
                "code": ResultCode.PARAM_IS_BLANK.code,
                "msg": "group_name不能为空",
                "data": []
            })

        if group_by not in ['dimension_id', 'data_tag_id']:
            return jsonify({
                "code": ResultCode.PARAM_ERROR.code,
                "msg": "group_by参数错误，只支持dimension_id或data_tag_id",
                "data": []
            })

        # 获取折线图数据
        line_chart_data = get_dimension_metrics_line_chart_data(
            task_result_ids=task_result_ids,
            task_ids=task_ids,
            group_by=group_by,
            group_name=group_name
        )

        return ApiResult(
            ResultCode.SUCCESS.code,
            ResultCode.SUCCESS.msg,
            line_chart_data
        ).to_json()

    except Exception as e:
        et_log.error(f"dimension_metrics_line_chart error: {e}")
        et_log.error(traceback.format_exc())
        return ApiResult(
            ResultCode.FAILURE.code,
            f"获取维度指标折线图数据失败: {str(e)}",
            []
        ).to_json()

